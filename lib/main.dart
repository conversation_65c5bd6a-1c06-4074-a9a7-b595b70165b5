import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/eg_firebase_options.dart';
import 'package:opti4t_tasks/src/base_app.dart';
import 'package:xr_helper/xr_helper.dart';

import 'ksa_firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Future.wait([
    Firebase.initializeApp(
        options: EGFirebaseOptions.currentPlatform, name: "EG"),
    Firebase.initializeApp(
        options: KSAFirebaseOptions.currentPlatform, name: "KSA"),
    Firebase.initializeApp(
        options: KSAFirebaseOptions.currentPlatform, name: "KSA"),
    GetStorageService.init(),
  ]);

  NotificationService.init();

  runApp(const ProviderScope(child: BaseApp()));
}
