import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/extensions/riverpod_extensions.dart';
import 'package:opti4t_tasks/src/core/services/firebase/firestore_service.dart';
import 'package:opti4t_tasks/src/core/services/notifications/local_notifications_service.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employees_page/widgets/employee_card/employee_card.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/bottom_nav_bar_widget.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/approve_tasks_floating_button.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/floating_buttons/check_in_out_floating_button.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/widgets/selected_widget.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../attendance/providers/attendance_providers.dart';

class HomeScreen extends HookConsumerWidget {
  final ValueNotifier<UserModel?>? selectedEmployeeValue;
  final ValueNotifier<DateTime>? selectedDayValue;

  const HomeScreen({
    super.key,
    this.selectedEmployeeValue,
    this.selectedDayValue,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentUser = ref.watch(getCurrentUserStreamProvider(context));

    final currentIndex = useState<int>(0);
    final selectedEmployee =
        selectedEmployeeValue ?? useState<UserModel?>(null);

    final selectedDay =
        useState<DateTime>(selectedDayValue?.value ?? DateTime.now());

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!UserModelHelper.isManager()) {
          LocalNotificationService.subscribe(context);
        }

        final userEmailName = UserModelHelper.signedUser().email.split('@')[0];

        NotificationService.subscribeToTopic(userEmailName);
      });

      return () {};
    }, []);

    return PopScope(onPopInvoked: (_) {
      exit(0);
    }, child: currentUser.get(
      data: (data) {
        if (data.firstOrNull?.isActive == false) {
          return Scaffold(
              body: Center(
            child: Padding(
              padding: const EdgeInsets.all(AppSpaces.largePadding),
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircleAvatar(
                      backgroundColor: ColorManager.errorColor,
                      maxRadius: 60,
                      child: Icon(
                        Icons.error,
                        color: Colors.white,
                        size: 50,
                      ),
                    ),
                    context.largeGap,
                    Text(
                      context.tr.accountDeactivated,
                      style: context.whiteSubHeadLine,
                      textAlign: TextAlign.center,
                    ),
                  ]),
            ),
          ));
        }
        return Scaffold(
            floatingActionButton: currentIndex.value == 0 &&
                    UserModelHelper.isManager()
                ? Builder(builder: (context) {
                    final params = (context, now, UserModelHelper.localUID());

                    final getLastAttendanceFuture = ref
                        .watch(getAttendancesStreamControllerProvider(params));

                    final lastAttendance = getLastAttendanceFuture.when(
                      data: (attendance) => attendance,
                      loading: () => null,
                      error: (error, _) => null,
                    );

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CheckInOutFloatingButton(
                            lastAttendance: lastAttendance?.lastOrNull),
                        ApproveTaskFloatingButton(
                          selectedEmployee: selectedEmployee,
                          selectedDayValue: selectedDay,
                        ),
                      ],
                    );
                  })
                : null,
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerFloat,
            bottomNavigationBar: BottomNavBarWidget(
              currentIndex: currentIndex,
            ),
            appBar: UserModelHelper.isManager()
                ? PreferredSize(
                    preferredSize: Size.fromHeight(60),
                    child: AppBar(
                      automaticallyImplyLeading: false,
                      backgroundColor: Colors.transparent,
                      surfaceTintColor: Colors.transparent,
                      title: BaseDropDown(
                        isWhiteText: true,
                        onChanged: (val) async {
                          isEgApp.value = val == context.tr.egApp;

                          GetStorageService.setLocalData(
                            key: LocalKeys.isEgApp,
                            value: isEgApp.value,
                          );

                          ref
                              .read(authControllerNotifierProvider(context))
                              .login(
                                email: AppConsts.managerAuthEmail,
                                password: AppConsts.managerAuthPass,
                              )
                              .then(
                            (value) {
                              if (value) {
                                Restart.restartApp(

                                    // Customizing the restart notification message (only needed on iOS)
                                    // notificationTitle: 'Restarting App',
                                    // notificationBody: 'Please tap here to open the app again.',
                                    );
                              }
                            },
                          );

                          // if (context.mounted && loggedIn) {
                          //   context.toReplacement(const HomeScreen());
                          // }
                        },
                        data: [
                          context.tr.egApp,
                          context.tr.ksaApp,
                        ],
                        label: context.tr.currentApp,
                        selectedValue: isEgApp.value
                            ? context.tr.egApp
                            : context.tr.ksaApp,
                      ),
                    ),
                  )
                : null,
            body: SelectedWidget(
              currentIndex: currentIndex,
              selectedEmployee: selectedEmployee,
              selectedDayValue: selectedDay,
            ));
      },
    ));
  }
}
